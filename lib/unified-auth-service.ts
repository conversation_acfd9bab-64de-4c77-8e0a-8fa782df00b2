/**
 * 统一认证服务
 * 提供统一的用户认证逻辑，整合NextAuth和AuthService
 *
 * 该服务确保所有API使用相同的认证模式，提高代码一致性和可维护性
 */

import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { AuthService } from "@/lib/auth-service";
import { verify } from "jsonwebtoken";
import { getToken } from "@/lib/auth";
import logger from '@/lib/utils/logger';

/**
 * 认证用户接口
 */
export interface AuthUser {
  id: string;
  email: string;
  username: string;
  roleCode: string;
  balance?: number;
  creditLimit?: number;
}

/**
 * 统一认证服务
 * 提供统一的用户认证方法
 */
export class UnifiedAuthService {
  /**
   * 获取当前用户
   * 首先尝试使用NextAuth，如果失败则尝试使用AuthService
   *
   * @param request NextRequest对象，可选
   * @param apiName API名称，用于日志记录
   * @returns 认证用户信息，如果未认证则返回null
   */
  static async getCurrentUser(request?: NextRequest, apiName: string = "API"): Promise<AuthUser | null> {
    try {
      logger.log(`[${apiName}] 开始获取当前用户`);

      // 检查请求对象
      if (request) {
        logger.log(`[${apiName}] 收到请求对象`);

        // 使用增强的getToken函数从请求中获取令牌
        logger.log(`[${apiName}] 尝试从请求中获取令牌`);
        const tokenPayload = await getToken(request);

        if (tokenPayload) {
          logger.log(`[${apiName}] 成功获取令牌载荷:`, tokenPayload.id);

          // 从数据库获取用户信息
          const dbUser = await prisma.user.findUnique({
            where: { id: tokenPayload.id },
            select: { id: true, email: true, username: true, roleCode: true, balance: true, creditLimit: true }
          });

          if (dbUser) {
            logger.log(`[${apiName}] 从数据库获取到用户:`, dbUser.id);
            return {
              id: dbUser.id,
              email: dbUser.email,
              username: dbUser.username,
              roleCode: dbUser.roleCode,
              balance: dbUser.balance,
              creditLimit: dbUser.creditLimit
            };
          } else {
            logger.error(`[${apiName}] 未找到用户ID为 ${tokenPayload.id} 的用户`);
          }
        } else {
          logger.log(`[${apiName}] 未从请求中获取到有效令牌`);
        }
      } else {
        logger.log(`[${apiName}] 未收到请求对象`);
      }

      // 先尝试使用NextAuth
      logger.log(`[${apiName}] 尝试使用NextAuth获取会话`);
      const { getServerSession } = await import("next-auth/next");
      const { options } = await import("@/app/api/auth/[...nextauth]/options");

      let user = null;

      try {
        // 在API路由中，getServerSession不需要传入请求对象
        // NextAuth会自动从请求上下文中获取会话信息
        const session = await getServerSession(options);

        // 详细记录会话信息，但不包含敏感数据
        logger.debug(`[${apiName}] NextAuth会话状态:`, {
          exists: !!session,
          hasUser: !!session?.user,
          hasId: !!session?.user?.id,
          hasEmail: !!session?.user?.email,
          hasRoleCode: !!session?.user?.roleCode,
          expires: session?.expires
        });

        // 如果有NextAuth会话，使用它
        if (session?.user?.id || session?.user?.email) {
          logger.log(`[${apiName}] 使用NextAuth会话获取用户`);

          // 如果有用户ID，直接使用
          if (session.user.id) {
            // 从数据库获取完整的用户信息，包括余额和授信额度
            const dbUser = await prisma.user.findUnique({
              where: { id: session.user.id },
              select: { id: true, email: true, username: true, roleCode: true, balance: true, creditLimit: true }
            });

            if (dbUser) {
              user = {
                id: dbUser.id,
                email: dbUser.email,
                username: dbUser.username,
                roleCode: dbUser.roleCode,
                balance: dbUser.balance,
                creditLimit: dbUser.creditLimit
              };
            } else {
              user = {
                id: session.user.id,
                email: session.user.email || "",
                username: session.user.name || "",
                roleCode: session.user.roleCode || ""
              };
            }
            logger.log(`[${apiName}] 从会话中获取到用户ID:`, user.id);
          }
          // 如果只有电子邮件，从数据库查询用户
          else if (session.user.email) {
            logger.log(`[${apiName}] 使用电子邮件查询用户:`, session.user.email);
            const dbUser = await prisma.user.findUnique({
              where: { email: session.user.email },
              select: { id: true, email: true, username: true, roleCode: true, balance: true, creditLimit: true }
            });

            if (dbUser) {
              user = {
                id: dbUser.id,
                email: dbUser.email,
                username: dbUser.username,
                roleCode: dbUser.roleCode,
                balance: dbUser.balance,
                creditLimit: dbUser.creditLimit
              };
              logger.log(`[${apiName}] 通过邮箱从数据库获取到用户:`, user.id);
            } else {
              logger.log(`[${apiName}] 未找到与邮箱 ${session.user.email} 匹配的用户`);
            }
          }
        } else {
          logger.log(`[${apiName}] NextAuth会话中没有用户信息`);
        }
      } catch (sessionError) {
        logger.error(`[${apiName}] 获取NextAuth会话时出错:`, sessionError);
      }

      // 如果NextAuth失败，尝试使用AuthService
      if (!user) {
        logger.log(`[${apiName}] NextAuth获取用户失败，尝试使用AuthService`);
        try {
          user = await AuthService.getCurrentUser();
          logger.log(`[${apiName}] AuthService获取用户结果:`, user ? user.id : "null");
        } catch (authError) {
          logger.error(`[${apiName}] AuthService获取用户失败:`, authError);
        }
      }

      // 如果仍然没有用户，返回null
      if (!user) {
        logger.error(`[${apiName}] 所有认证方式均失败`);
        return null;
      }

      logger.log(`[${apiName}] 成功获取用户:`, { id: user.id, username: user.username, roleCode: user.roleCode });
      return user;
    } catch (error) {
      logger.error(`[${apiName}] 获取用户时出错:`, error);
      return null;
    }
  }

  /**
   * 检查用户是否为管理员
   * 支持大小写的角色代码检查
   *
   * @param user 用户对象
   * @param apiName API名称，用于日志记录
   * @returns 如果用户是管理员返回true，否则返回false
   */
  static isAdmin(user: AuthUser | null, apiName: string = "API"): boolean {
    if (!user) {
      logger.log(`[${apiName}] 用户为空，非管理员`);
      return false;
    }

    // 将角色代码转换为大写进行比较，避免大小写问题
    const roleCode = user.roleCode?.toUpperCase() || '';
    const isAdmin = roleCode === 'ADMIN' || roleCode === 'SUPER';

    logger.log(`[${apiName}] 用户角色代码: ${roleCode}, 是否管理员: ${isAdmin}`);
    return isAdmin;
  }

  /**
   * 检查用户是否有指定角色
   * 支持大小写的角色代码检查
   *
   * @param user 用户对象
   * @param role 角色代码
   * @param apiName API名称，用于日志记录
   * @returns 如果用户有指定角色返回true，否则返回false
   */
  static hasRole(user: AuthUser | null, role: string, apiName: string = "API"): boolean {
    if (!user) {
      logger.log(`[${apiName}] 用户为空，无指定角色`);
      return false;
    }

    // 将角色代码转换为大写进行比较，避免大小写问题
    const userRoleCode = user.roleCode?.toUpperCase() || '';
    const targetRole = role.toUpperCase();
    const hasRole = userRoleCode === targetRole;

    logger.log(`[${apiName}] 用户角色代码: ${userRoleCode}, 目标角色: ${targetRole}, 是否匹配: ${hasRole}`);
    return hasRole;
  }

  /**
   * 检查用户是否有多个角色中的任一个
   * 支持大小写的角色代码检查
   *
   * @param user 用户对象
   * @param roles 角色代码数组
   * @param apiName API名称，用于日志记录
   * @returns 如果用户有指定角色之一返回true，否则返回false
   */
  static hasAnyRole(user: AuthUser | null, roles: string[], apiName: string = "API"): boolean {
    if (!user) {
      logger.log(`[${apiName}] 用户为空，无指定角色`);
      return false;
    }

    // 将用户角色代码转换为大写
    const userRoleCode = user.roleCode?.toUpperCase() || '';

    // 将目标角色代码转换为大写
    const targetRoles = roles.map(role => role.toUpperCase());

    // 检查用户是否有指定角色之一
    const hasRole = targetRoles.includes(userRoleCode);

    logger.log(`[${apiName}] 用户角色代码: ${userRoleCode}, 目标角色: ${targetRoles.join(', ')}, 是否匹配: ${hasRole}`);
    return hasRole;
  }

  /**
   * 检查用户是否有权限访问管理员API
   * 如果用户不是管理员，返回错误响应
   *
   * @param user 用户对象
   * @param apiName API名称，用于日志记录
   * @returns 如果用户有权限返回true，否则返回false
   */
  static hasAdminAccess(user: AuthUser | null, apiName: string = "API"): boolean {
    const isAdmin = this.isAdmin(user, apiName);

    if (!isAdmin) {
      logger.error(`[${apiName}] 用户没有管理员权限:`, user?.username, user?.roleCode);
    }

    return isAdmin;
  }
}
